package com.biligame.activity.community.application.command;

/**
 * <AUTHOR>
 * @create 2025/5/27 11:08
 * @description 咒术角色强化命令服务
 */
public interface JuJuCharacterEnhanceCmdService {

    /**
     * 根据玩家角色卡片战力等级发放奖励
     * @param roleId 角色ID
     * @param cardId 卡片ID
     * @param rewardId 奖励ID
     * @return 奖励发放结果，包含订单号等信息
     */
    Object distributeRewardByPowerLevel(Long roleId, Long cardId, Long rewardId);
}
