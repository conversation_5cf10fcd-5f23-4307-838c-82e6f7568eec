package com.biligame.activity.community.application.command.impl;

import com.biligame.activity.community.application.command.JuJuCharacterEnhanceCmdService;
import com.biligame.activity.community.infrastructure.idempotent.IdempotentKvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 2025/5/27 11:52
 * @description 咒术角色强化命令服务实现
 */
@Slf4j
@Service
public class JuJuCharacterEnhanceCmdServiceImpl implements JuJuCharacterEnhanceCmdService {

    @Resource
    private IdempotentKvService idempotentKvService;

    @Override
    public Object distributeRewardByPowerLevel(Long roleId, Long cardId, Long rewardId) {
        log.info("开始处理角色战力奖励发放: roleId={}, cardId={}, rewardId={}", roleId, cardId, rewardId);

        // 1. 构建幂等键
        String idempotentKey = idempotentKvService.buildKey("POWER_REWARD",
                String.valueOf(roleId), String.valueOf(cardId), String.valueOf(rewardId));

        // 2. 检查是否已经处理过
        String existingOrderNo = idempotentKvService.get(idempotentKey);
        if (existingOrderNo != null) {
            log.info("奖励已发放，返回已有订单号: roleId={}, cardId={}, rewardId={}, orderNo={}",
                    roleId, cardId, rewardId, existingOrderNo);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "奖励已发放");
            result.put("orderNo", existingOrderNo);
            result.put("duplicate", true);
            return result;
        }

        // 3. 生成订单号
        String orderNo = generateOrderNo();

        // 4. 尝试设置幂等键（原子操作）
        boolean setSuccess = idempotentKvService.trySet(idempotentKey, orderNo);

        if (!setSuccess) {
            // 并发情况下，其他线程已经处理了，获取已有的订单号
            existingOrderNo = idempotentKvService.get(idempotentKey);
            log.info("并发处理，返回已有订单号: roleId={}, cardId={}, rewardId={}, orderNo={}",
                    roleId, cardId, rewardId, existingOrderNo);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "奖励已发放");
            result.put("orderNo", existingOrderNo != null ? existingOrderNo : "UNKNOWN");
            result.put("duplicate", true);
            return result;
        }

        try {
            // 5. 执行实际的业务逻辑
            RewardInfo rewardInfo = processReward(roleId, cardId, rewardId, orderNo);

            // 6. 将奖励信息存入发奖表
            saveRewardToDatabase(rewardInfo);

            log.info("奖励发放成功: roleId={}, cardId={}, rewardId={}, orderNo={}",
                    roleId, cardId, rewardId, orderNo);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "奖励发放成功");
            result.put("orderNo", orderNo);
            result.put("duplicate", false);
            result.put("rewardInfo", rewardInfo);
            return result;

        } catch (Exception e) {
            log.error("奖励发放失败: roleId={}, cardId={}, rewardId={}, orderNo={}",
                    roleId, cardId, rewardId, orderNo, e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "奖励发放失败: " + e.getMessage());
            result.put("orderNo", orderNo);
            return result;
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "PWR_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * 处理奖励逻辑
     */
    private RewardInfo processReward(Long roleId, Long cardId, Long rewardId, String orderNo) {
        log.info("处理奖励逻辑: roleId={}, cardId={}, rewardId={}, orderNo={}",
                roleId, cardId, rewardId, orderNo);

        // 模拟业务处理
        RewardInfo rewardInfo = new RewardInfo();
        rewardInfo.setRoleId(roleId);
        rewardInfo.setCardId(cardId);
        rewardInfo.setRewardId(rewardId);
        rewardInfo.setOrderNo(orderNo);
        rewardInfo.setRewardType("POWER_LEVEL_REWARD");
        rewardInfo.setRewardAmount(1000);
        rewardInfo.setRewardDesc("战力等级奖励");

        return rewardInfo;
    }

    /**
     * 将奖励信息存入发奖表
     */
    private void saveRewardToDatabase(RewardInfo rewardInfo) {
        log.info("保存奖励信息到数据库: {}", rewardInfo);
        // 这里实现将奖励信息存入数据库的逻辑
        // rewardMapper.insert(rewardInfo);
    }

    /**
     * 奖励信息类
     */
    public static class RewardInfo {
        private Long roleId;
        private Long cardId;
        private Long rewardId;
        private String orderNo;
        private String rewardType;
        private Integer rewardAmount;
        private String rewardDesc;

        // getters and setters
        public Long getRoleId() { return roleId; }
        public void setRoleId(Long roleId) { this.roleId = roleId; }
        public Long getCardId() { return cardId; }
        public void setCardId(Long cardId) { this.cardId = cardId; }
        public Long getRewardId() { return rewardId; }
        public void setRewardId(Long rewardId) { this.rewardId = rewardId; }
        public String getOrderNo() { return orderNo; }
        public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
        public String getRewardType() { return rewardType; }
        public void setRewardType(String rewardType) { this.rewardType = rewardType; }
        public Integer getRewardAmount() { return rewardAmount; }
        public void setRewardAmount(Integer rewardAmount) { this.rewardAmount = rewardAmount; }
        public String getRewardDesc() { return rewardDesc; }
        public void setRewardDesc(String rewardDesc) { this.rewardDesc = rewardDesc; }

        @Override
        public String toString() {
            return "RewardInfo{" +
                    "roleId=" + roleId +
                    ", cardId=" + cardId +
                    ", rewardId=" + rewardId +
                    ", orderNo='" + orderNo + '\'' +
                    ", rewardType='" + rewardType + '\'' +
                    ", rewardAmount=" + rewardAmount +
                    ", rewardDesc='" + rewardDesc + '\'' +
                    '}';
        }
    }
}
