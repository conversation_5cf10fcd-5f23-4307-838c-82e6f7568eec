package com.biligame.activity.community.infrastructure.db.po;

import com.biligame.activity.community.shared.dataobject.BaseDO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025/5/27 12:30
 * @description 幂等KV存储PO
 */
@Getter
@Setter
public class IdempotentKvPO extends BaseDO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 幂等键
     */
    private String idempotentKey;

    /**
     * 存储值
     */
    private String value;



    /**
     * 删除标记（Y/N）
     */
    private String isDeleted;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}
