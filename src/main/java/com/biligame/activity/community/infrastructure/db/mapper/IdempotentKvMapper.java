package com.biligame.activity.community.infrastructure.db.mapper;

import com.biligame.activity.community.infrastructure.db.po.IdempotentKvPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025/5/27 12:30
 * @description 幂等KV存储Mapper
 */
@Mapper
public interface IdempotentKvMapper {

    /**
     * 插入KV记录（幂等）
     * @param record KV记录
     * @return 影响行数
     */
    int insert(IdempotentKvPO record);

    /**
     * 根据key查询value
     * @param key 幂等键
     * @return 存储值
     */
    String selectValueByKey(@Param("key") String key);

    /**
     * 检查key是否存在
     * @param key 幂等键
     * @return 是否存在
     */
    boolean existsByKey(@Param("key") String key);
}
