package com.biligame.activity.community.infrastructure.idempotent;

/**
 * <AUTHOR>
 * @create 2025/5/27 12:30
 * @description 幂等KV存储服务
 */
public interface IdempotentKvService {

    /**
     * 尝试设置KV（幂等操作）
     * @param key 幂等键
     * @param value 存储值
     * @return 是否设置成功（true=首次设置成功，false=已存在）
     */
    boolean trySet(String key, String value);

    /**
     * 获取值
     * @param key 幂等键
     * @return 存储的值，不存在或过期返回null
     */
    String get(String key);

    /**
     * 检查key是否存在
     * @param key 幂等键
     * @return 是否存在且未过期
     */
    boolean exists(String key);

    /**
     * 构建幂等键
     * @param parts 键的组成部分
     * @return 幂等键
     */
    String buildKey(String... parts);

    /**
     * 清理过期记录
     * @return 清理的记录数
     */
    int cleanExpiredRecords();
}
