-- 幂等KV存储表
CREATE TABLE `idempotent_kv` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `idempotent_key` varchar(255) NOT NULL COMMENT '幂等键',
  `value` varchar(1024) NOT NULL COMMENT '存储值',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记（Y/N）',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_idempotent_key` (`idempotent_key`),
  KEY `idx_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='幂等KV存储表';
