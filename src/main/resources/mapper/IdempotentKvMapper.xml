<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biligame.activity.community.infrastructure.db.mapper.IdempotentKvMapper">

    <insert id="insert" parameterType="com.biligame.activity.community.infrastructure.db.po.IdempotentKvPO">
        INSERT INTO idempotent_kv (
            idempotent_key, value, is_deleted, ctime, mtime
        ) VALUES (
            #{idempotentKey}, #{value}, #{isDeleted}, #{ctime}, #{mtime}
        )
    </insert>

    <select id="selectValueByKey" resultType="java.lang.String">
        SELECT value
        FROM idempotent_kv
        WHERE idempotent_key = #{key}
        AND is_deleted = 'N'
    </select>

    <select id="existsByKey" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM idempotent_kv
        WHERE idempotent_key = #{key}
        AND is_deleted = 'N'
    </select>

</mapper>
